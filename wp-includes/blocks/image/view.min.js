import*as t from"@wordpress/interactivity";var e={d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const n=(t=>{var n={};return e.d(n,t),n})({getContext:()=>t.getContext,getElement:()=>t.getElement,store:()=>t.store});let o,r,i=!1,l=0;const{state:a,actions:c,callbacks:s}=(0,n.store)("core/image",{state:{currentImage:{},get overlayOpened(){return a.currentImage.currentSrc},get roleAttribute(){return a.overlayOpened?"dialog":null},get ariaModal(){return a.overlayOpened?"true":null},get enlargedSrc(){return a.currentImage.uploadedSrc||"data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="},get imgStyles(){return a.overlayOpened&&`${a.currentImage.imgStyles?.replace(/;$/,"")}; object-fit:cover;`}},actions:{showLightbox(){const t=(0,n.getContext)();t.imageRef?.complete&&(a.scrollTopReset=document.documentElement.scrollTop,a.scrollLeftReset=document.documentElement.scrollLeft,t.currentSrc=t.imageRef.currentSrc,o=t.imageRef,r=t.buttonRef,a.currentImage=t,a.overlayEnabled=!0,s.setOverlayStyles())},hideLightbox(){a.overlayEnabled&&(setTimeout((function(){r.focus({preventScroll:!0}),a.currentImage={},o=null,r=null}),450),a.showClosingAnimation=!0,a.overlayEnabled=!1)},handleKeydown(t){if(a.overlayEnabled){if("Tab"===t.key){t.preventDefault();const{ref:e}=(0,n.getElement)();e.querySelector("button").focus()}"Escape"===t.key&&c.hideLightbox()}},handleTouchMove(t){a.overlayEnabled&&t.preventDefault()},handleTouchStart(){i=!0},handleTouchEnd(){l=Date.now(),i=!1},handleScroll(){a.overlayOpened&&!i&&Date.now()-l>450&&window.scrollTo(a.scrollLeftReset,a.scrollTopReset)}},callbacks:{setOverlayStyles(){if(!o)return;let{naturalWidth:t,naturalHeight:e,offsetWidth:n,offsetHeight:r}=o,{x:i,y:l}=o.getBoundingClientRect();const c=t/e;let s=n/r;if("contain"===a.currentImage.scaleAttr)if(c>s){const t=n/c;l+=(r-t)/2,r=t}else{const t=r*c;i+=(n-t)/2,n=t}s=n/r;let g=parseFloat("none"!==a.currentImage.targetWidth?a.currentImage.targetWidth:t),u=parseFloat("none"!==a.currentImage.targetHeight?a.currentImage.targetHeight:e),d=g/u,h=g,m=u,p=g,f=u;if(c.toFixed(2)!==d.toFixed(2)){if(c>d){const t=g/c;u-t>g?(u=t,g=t*c):u=g/c}else{const t=u*c;g-t>u?(g=t,u=t/c):g=u*c}p=g,f=u,d=g/u,s>d?(h=g,m=h/s):(m=u,h=m*s)}(n>p||r>f)&&(p=n,f=r);let w=0;window.innerWidth>480?w=80:window.innerWidth>1920&&(w=160);const y=Math.min(window.innerWidth-w,p),b=Math.min(window.innerHeight-80,f);s>y/b?(p=y,f=p/s):(f=b,p=f*s);const x=n/p,v=g*(p/h),A=u*(f/m);a.overlayStyles=`\n\t\t\t\t:root {\n\t\t\t\t\t--wp--lightbox-initial-top-position: ${l}px;\n\t\t\t\t\t--wp--lightbox-initial-left-position: ${i}px;\n\t\t\t\t\t--wp--lightbox-container-width: ${p+1}px;\n\t\t\t\t\t--wp--lightbox-container-height: ${f+1}px;\n\t\t\t\t\t--wp--lightbox-image-width: ${v}px;\n\t\t\t\t\t--wp--lightbox-image-height: ${A}px;\n\t\t\t\t\t--wp--lightbox-scale: ${x};\n\t\t\t\t\t--wp--lightbox-scrollbar-width: ${window.innerWidth-document.documentElement.clientWidth}px;\n\t\t\t\t}\n\t\t\t`},setButtonStyles(){const t=(0,n.getContext)(),{ref:e}=(0,n.getElement)();t.imageRef=e;const{naturalWidth:o,naturalHeight:r,offsetWidth:i,offsetHeight:l}=e;if(0===o||0===r)return;const a=e.parentElement,c=e.parentElement.clientWidth;let s=e.parentElement.clientHeight;const g=a.querySelector("figcaption");if(g){const t=window.getComputedStyle(g);["absolute","fixed"].includes(t.position)||(s=s-g.offsetHeight-parseFloat(t.marginTop)-parseFloat(t.marginBottom))}const u=s-l,d=c-i;if("contain"===t.scaleAttr){const e=o/r;if(e>=i/l){const n=i/e;t.imageButtonTop=(l-n)/2+u+16,t.imageButtonRight=d+16}else{const n=l*e;t.imageButtonTop=u+16,t.imageButtonRight=(i-n)/2+d+16}}else t.imageButtonTop=u+16,t.imageButtonRight=d+16},setOverlayFocus(){if(a.overlayEnabled){const{ref:t}=(0,n.getElement)();t.focus()}},initTriggerButton(){const t=(0,n.getContext)(),{ref:e}=(0,n.getElement)();t.buttonRef=e}}},{lock:!0});