/*! This file is auto-generated */
(()=>{var e={5755:(e,t)=>{var r;
/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var s={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var n=typeof r;if("string"===n||"number"===n)e.push(r);else if(Array.isArray(r)){if(r.length){var o=i.apply(null,r);o&&e.push(o)}}else if("object"===n){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var a in r)s.call(r,a)&&r[a]&&e.push(a)}}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0===(r=function(){return i}.apply(t,[]))||(e.exports=r)}()},7734:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var s,i,n;if(Array.isArray(t)){if((s=t.length)!=r.length)return!1;for(i=s;0!=i--;)if(!e(t[i],r[i]))return!1;return!0}if(t instanceof Map&&r instanceof Map){if(t.size!==r.size)return!1;for(i of t.entries())if(!r.has(i[0]))return!1;for(i of t.entries())if(!e(i[1],r.get(i[0])))return!1;return!0}if(t instanceof Set&&r instanceof Set){if(t.size!==r.size)return!1;for(i of t.entries())if(!r.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(r)){if((s=t.length)!=r.length)return!1;for(i=s;0!=i--;)if(t[i]!==r[i])return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((s=(n=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(i=s;0!=i--;)if(!Object.prototype.hasOwnProperty.call(r,n[i]))return!1;for(i=s;0!=i--;){var o=n[i];if(!e(t[o],r[o]))return!1}return!0}return t!=t&&r!=r}}},t={};function r(s){var i=t[s];if(void 0!==i)return i.exports;var n=t[s]={exports:{}};return e[s](n,n.exports,r),n.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};(()=>{"use strict";r.r(s),r.d(s,{initialize:()=>st,store:()=>F});var e={};r.r(e),r.d(e,{__experimentalGetInsertionPoint:()=>T,isInserterOpened:()=>O});var t={};r.r(t),r.d(t,{setIsInserterOpened:()=>N});var i={};r.r(i),r.d(i,{closeModal:()=>ee,disableComplementaryArea:()=>Y,enableComplementaryArea:()=>j,openModal:()=>X,pinItem:()=>q,setDefaultComplementaryArea:()=>$,setFeatureDefaults:()=>Q,setFeatureValue:()=>J,toggleFeature:()=>Z,unpinItem:()=>K});var n={};r.r(n),r.d(n,{getActiveComplementaryArea:()=>te,isComplementaryAreaLoading:()=>re,isFeatureActive:()=>ie,isItemPinned:()=>se,isModalActive:()=>ne});const o=window.React,a=window.wp.element,c=window.wp.blockLibrary,l=window.wp.widgets,d=window.wp.blocks,u=window.wp.data,m=window.wp.preferences,h=window.wp.components,p=window.wp.i18n,g=window.wp.blockEditor,f=window.wp.compose,b=window.wp.hooks;function w({text:e,children:t}){const r=(0,f.useCopyToClipboard)(e);return(0,o.createElement)(h.Button,{variant:"secondary",ref:r},t)}class _ extends a.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e){this.setState({error:e}),(0,b.doAction)("editor.ErrorBoundary.errorLogged",e)}render(){const{error:e}=this.state;return e?(0,o.createElement)(g.Warning,{className:"customize-widgets-error-boundary",actions:[(0,o.createElement)(w,{key:"copy-error",text:e.stack},(0,p.__)("Copy Error"))]},(0,p.__)("The editor has encountered an unexpected error.")):this.props.children}}const y=window.wp.coreData,E=window.wp.mediaUtils;const v=function({inspector:e,closeMenu:t,...r}){const s=(0,u.useSelect)((e=>e(g.store).getSelectedBlockClientId()),[]),i=(0,a.useMemo)((()=>document.getElementById(`block-${s}`)),[s]);return(0,o.createElement)(h.MenuItem,{onClick:()=>{e.open({returnFocusWhenClose:i}),t()},...r},(0,p.__)("Show more settings"))};var k=r(5755),C=r.n(k);const S=window.wp.keycodes,x=window.wp.primitives,I=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"})),z=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"})),A=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})),B=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"}));const W=(0,u.combineReducers)({blockInserterPanel:function(e=!1,t){return"SET_IS_INSERTER_OPENED"===t.type?t.value:e}}),M={rootClientId:void 0,insertionIndex:void 0};function O(e){return!!e.blockInserterPanel}function T(e){return"boolean"==typeof e.blockInserterPanel?M:e.blockInserterPanel}function N(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}const P={reducer:W,selectors:e,actions:t},F=(0,u.createReduxStore)("core/customize-widgets",P);(0,u.register)(F);const L=function e({setIsOpened:t}){const r=(0,f.useInstanceId)(e,"customize-widget-layout__inserter-panel-title"),s=(0,u.useSelect)((e=>e(F).__experimentalGetInsertionPoint()),[]);return(0,o.createElement)("div",{className:"customize-widgets-layout__inserter-panel","aria-labelledby":r},(0,o.createElement)("div",{className:"customize-widgets-layout__inserter-panel-header"},(0,o.createElement)("h2",{id:r,className:"customize-widgets-layout__inserter-panel-header-title"},(0,p.__)("Add a block")),(0,o.createElement)(h.Button,{className:"customize-widgets-layout__inserter-panel-header-close-button",icon:B,onClick:()=>t(!1),"aria-label":(0,p.__)("Close inserter")})),(0,o.createElement)("div",{className:"customize-widgets-layout__inserter-panel-content"},(0,o.createElement)(g.__experimentalLibrary,{rootClientId:s.rootClientId,__experimentalInsertionIndex:s.insertionIndex,showInserterHelpPanel:!0,onSelect:()=>t(!1)})))},D=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})),R=window.wp.keyboardShortcuts,V=(0,o.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(x.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}));function H({as:e=h.DropdownMenu,className:t,label:r=(0,p.__)("Options"),popoverProps:s,toggleProps:i,children:n}){return(0,o.createElement)(e,{className:C()("interface-more-menu-dropdown",t),icon:V,label:r,popoverProps:{placement:"bottom-end",...s,className:C()("interface-more-menu-dropdown__content",s?.className)},toggleProps:{tooltipPosition:"bottom",...i,size:"compact"}},(e=>n(e)))}const G=window.wp.deprecated;var U=r.n(G);const $=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e,area:t}),j=(e,t)=>({registry:r,dispatch:s})=>{if(!t)return;r.select(m.store).get(e,"isComplementaryAreaVisible")||r.dispatch(m.store).set(e,"isComplementaryAreaVisible",!0),s({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t})},Y=e=>({registry:t})=>{t.select(m.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(m.store).set(e,"isComplementaryAreaVisible",!1)},q=(e,t)=>({registry:r})=>{if(!t)return;const s=r.select(m.store).get(e,"pinnedItems");!0!==s?.[t]&&r.dispatch(m.store).set(e,"pinnedItems",{...s,[t]:!0})},K=(e,t)=>({registry:r})=>{if(!t)return;const s=r.select(m.store).get(e,"pinnedItems");r.dispatch(m.store).set(e,"pinnedItems",{...s,[t]:!1})};function Z(e,t){return function({registry:r}){U()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),r.dispatch(m.store).toggle(e,t)}}function J(e,t,r){return function({registry:s}){U()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),s.dispatch(m.store).set(e,t,!!r)}}function Q(e,t){return function({registry:r}){U()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),r.dispatch(m.store).setDefaults(e,t)}}function X(e){return{type:"OPEN_MODAL",name:e}}function ee(){return{type:"CLOSE_MODAL"}}const te=(0,u.createRegistrySelector)((e=>(t,r)=>{const s=e(m.store).get(r,"isComplementaryAreaVisible");if(void 0!==s)return!1===s?null:t?.complementaryAreas?.[r]})),re=(0,u.createRegistrySelector)((e=>(t,r)=>{const s=e(m.store).get(r,"isComplementaryAreaVisible"),i=t?.complementaryAreas?.[r];return s&&void 0===i})),se=(0,u.createRegistrySelector)((e=>(t,r,s)=>{var i;const n=e(m.store).get(r,"pinnedItems");return null===(i=n?.[s])||void 0===i||i})),ie=(0,u.createRegistrySelector)((e=>(t,r,s)=>(U()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(m.store).get(r,s))));function ne(e,t){return e.activeModal===t}const oe=(0,u.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:r,area:s}=t;return e[r]?e:{...e,[r]:s}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:r,area:s}=t;return{...e,[r]:s}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}}),ae=(0,u.createReduxStore)("core/interface",{reducer:oe,actions:i,selectors:n});(0,u.register)(ae);const ce=[{keyCombination:{modifier:"primary",character:"b"},description:(0,p.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,p.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,p.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,p.__)("Remove a link.")},{keyCombination:{character:"[["},description:(0,p.__)("Insert a link to a post or page.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,p.__)("Underline the selected text.")},{keyCombination:{modifier:"access",character:"d"},description:(0,p.__)("Strikethrough the selected text.")},{keyCombination:{modifier:"access",character:"x"},description:(0,p.__)("Make the selected text inline code.")},{keyCombination:{modifier:"access",character:"0"},description:(0,p.__)("Convert the current heading to a paragraph.")},{keyCombination:{modifier:"access",character:"1-6"},description:(0,p.__)("Convert the current paragraph or heading to a heading of level 1 to 6.")}];function le({keyCombination:e,forceAriaLabel:t}){const r=e.modifier?S.displayShortcutList[e.modifier](e.character):e.character,s=e.modifier?S.shortcutAriaLabel[e.modifier](e.character):e.character;return(0,o.createElement)("kbd",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":t||s},(Array.isArray(r)?r:[r]).map(((e,t)=>"+"===e?(0,o.createElement)(a.Fragment,{key:t},e):(0,o.createElement)("kbd",{key:t,className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-key"},e))))}const de=function({description:e,keyCombination:t,aliases:r=[],ariaLabel:s}){return(0,o.createElement)(a.Fragment,null,(0,o.createElement)("div",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-description"},e),(0,o.createElement)("div",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-term"},(0,o.createElement)(le,{keyCombination:t,forceAriaLabel:s}),r.map(((e,t)=>(0,o.createElement)(le,{keyCombination:e,forceAriaLabel:s,key:t})))))};const ue=function({name:e}){const{keyCombination:t,description:r,aliases:s}=(0,u.useSelect)((t=>{const{getShortcutKeyCombination:r,getShortcutDescription:s,getShortcutAliases:i}=t(R.store);return{keyCombination:r(e),aliases:i(e),description:s(e)}}),[e]);return t?(0,o.createElement)(de,{keyCombination:t,description:r,aliases:s}):null},me=({shortcuts:e})=>(0,o.createElement)("ul",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-list",role:"list"},e.map(((e,t)=>(0,o.createElement)("li",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut",key:t},"string"==typeof e?(0,o.createElement)(ue,{name:e}):(0,o.createElement)(de,{...e}))))),he=({title:e,shortcuts:t,className:r})=>(0,o.createElement)("section",{className:C()("customize-widgets-keyboard-shortcut-help-modal__section",r)},!!e&&(0,o.createElement)("h2",{className:"customize-widgets-keyboard-shortcut-help-modal__section-title"},e),(0,o.createElement)(me,{shortcuts:t})),pe=({title:e,categoryName:t,additionalShortcuts:r=[]})=>{const s=(0,u.useSelect)((e=>e(R.store).getCategoryShortcuts(t)),[t]);return(0,o.createElement)(he,{title:e,shortcuts:s.concat(r)})};function ge({isModalActive:e,toggleModal:t}){const{registerShortcut:r}=(0,u.useDispatch)(R.store);return r({name:"core/customize-widgets/keyboard-shortcuts",category:"main",description:(0,p.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),(0,R.useShortcut)("core/customize-widgets/keyboard-shortcuts",t),e?(0,o.createElement)(h.Modal,{className:"customize-widgets-keyboard-shortcut-help-modal",title:(0,p.__)("Keyboard shortcuts"),onRequestClose:t},(0,o.createElement)(he,{className:"customize-widgets-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/customize-widgets/keyboard-shortcuts"]}),(0,o.createElement)(pe,{title:(0,p.__)("Global shortcuts"),categoryName:"global"}),(0,o.createElement)(pe,{title:(0,p.__)("Selection shortcuts"),categoryName:"selection"}),(0,o.createElement)(pe,{title:(0,p.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,p.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,p.__)("Forward-slash")}]}),(0,o.createElement)(he,{title:(0,p.__)("Text formatting"),shortcuts:ce})):null}function fe(){const[e,t]=(0,a.useState)(!1),r=()=>t(!e);return(0,R.useShortcut)("core/customize-widgets/keyboard-shortcuts",r),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(H,{as:h.ToolbarDropdownMenu},(()=>(0,o.createElement)(o.Fragment,null,(0,o.createElement)(h.MenuGroup,{label:(0,p._x)("View","noun")},(0,o.createElement)(m.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"fixedToolbar",label:(0,p.__)("Top toolbar"),info:(0,p.__)("Access all block and document tools in a single place"),messageActivated:(0,p.__)("Top toolbar activated"),messageDeactivated:(0,p.__)("Top toolbar deactivated")})),(0,o.createElement)(h.MenuGroup,{label:(0,p.__)("Tools")},(0,o.createElement)(h.MenuItem,{onClick:()=>{t(!0)},shortcut:S.displayShortcut.access("h")},(0,p.__)("Keyboard shortcuts")),(0,o.createElement)(m.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"welcomeGuide",label:(0,p.__)("Welcome Guide")}),(0,o.createElement)(h.MenuItem,{role:"menuitem",icon:D,href:(0,p.__)("https://wordpress.org/documentation/article/block-based-widgets-editor/"),target:"_blank",rel:"noopener noreferrer"},(0,p.__)("Help"),(0,o.createElement)(h.VisuallyHidden,{as:"span"},(0,p.__)("(opens in a new tab)")))),(0,o.createElement)(h.MenuGroup,{label:(0,p.__)("Preferences")},(0,o.createElement)(m.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"keepCaretInsideBlock",label:(0,p.__)("Contain text cursor inside block"),info:(0,p.__)("Aids screen readers by stopping text caret from leaving blocks."),messageActivated:(0,p.__)("Contain text cursor inside block activated"),messageDeactivated:(0,p.__)("Contain text cursor inside block deactivated")}))))),(0,o.createElement)(ge,{isModalActive:e,toggleModal:r}))}const be=function({sidebar:e,inserter:t,isInserterOpened:r,setIsInserterOpened:s,isFixedToolbarActive:i}){const[[n,c],l]=(0,a.useState)([e.hasUndo(),e.hasRedo()]),d=(0,S.isAppleOS)()?S.displayShortcut.primaryShift("z"):S.displayShortcut.primary("y");return(0,a.useEffect)((()=>e.subscribeHistory((()=>{l([e.hasUndo(),e.hasRedo()])}))),[e]),(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:C()("customize-widgets-header",{"is-fixed-toolbar-active":i})},(0,o.createElement)(g.NavigableToolbar,{className:"customize-widgets-header-toolbar","aria-label":(0,p.__)("Document tools")},(0,o.createElement)(h.ToolbarButton,{icon:(0,p.isRTL)()?z:I,label:(0,p.__)("Undo"),shortcut:S.displayShortcut.primary("z"),"aria-disabled":!n,onClick:e.undo,className:"customize-widgets-editor-history-button undo-button"}),(0,o.createElement)(h.ToolbarButton,{icon:(0,p.isRTL)()?I:z,label:(0,p.__)("Redo"),shortcut:d,"aria-disabled":!c,onClick:e.redo,className:"customize-widgets-editor-history-button redo-button"}),(0,o.createElement)(h.ToolbarButton,{className:"customize-widgets-header-toolbar__inserter-toggle",isPressed:r,variant:"primary",icon:A,label:(0,p._x)("Add block","Generic label for block inserter button"),onClick:()=>{s((e=>!e))}}),(0,o.createElement)(fe,null))),(0,a.createPortal)((0,o.createElement)(L,{setIsOpened:s}),t.contentContainer[0]))};var we=r(7734),_e=r.n(we);const ye=window.wp.isShallowEqual;var Ee=r.n(ye);function ve(e){const t=e.match(/^widget_(.+)(?:\[(\d+)\])$/);if(t){return`${t[1]}-${parseInt(t[2],10)}`}return e}function ke(e,t=null){let r;if("core/legacy-widget"===e.name&&(e.attributes.id||e.attributes.instance))if(e.attributes.id)r={id:e.attributes.id};else{const{encoded:s,hash:i,raw:n,...o}=e.attributes.instance;r={idBase:e.attributes.idBase,instance:{...t?.instance,is_widget_customizer_js_value:!0,encoded_serialized_instance:s,instance_hash_key:i,raw_instance:n,...o}}}else{r={idBase:"block",widgetClass:"WP_Widget_Block",instance:{raw_instance:{content:(0,d.serialize)(e)}}}}const{form:s,rendered:i,...n}=t||{};return{...n,...r}}function Ce({id:e,idBase:t,number:r,instance:s}){let i;const{encoded_serialized_instance:n,instance_hash_key:o,raw_instance:a,...c}=s;if("block"===t){var u;const e=(0,d.parse)(null!==(u=a.content)&&void 0!==u?u:"",{__unstableSkipAutop:!0});i=e.length?e[0]:(0,d.createBlock)("core/paragraph",{})}else i=r?(0,d.createBlock)("core/legacy-widget",{idBase:t,instance:{encoded:n,hash:o,raw:a,...c}}):(0,d.createBlock)("core/legacy-widget",{id:e});return(0,l.addWidgetIdToBlock)(i,e)}function Se(e){const[t,r]=(0,a.useState)((()=>e.getWidgets().map((e=>Ce(e)))));(0,a.useEffect)((()=>e.subscribe(((e,t)=>{r((r=>{const s=new Map(e.map((e=>[e.id,e]))),i=new Map(r.map((e=>[(0,l.getWidgetIdFromBlock)(e),e]))),n=t.map((e=>{const t=s.get(e.id);return t&&t===e?i.get(e.id):Ce(e)}));return Ee()(r,n)?r:n}))}))),[e]);const s=(0,a.useCallback)((t=>{r((r=>{if(Ee()(r,t))return r;const s=new Map(r.map((e=>[(0,l.getWidgetIdFromBlock)(e),e]))),i=t.map((t=>{const r=(0,l.getWidgetIdFromBlock)(t);if(r&&s.has(r)){const i=s.get(r),n=e.getWidget(r);return _e()(t,i)&&n?n:ke(t,n)}return ke(t)}));if(Ee()(e.getWidgets(),i))return r;const n=e.setWidgets(i);return t.reduce(((e,r,s)=>{const i=n[s];return null!==i&&(e===t&&(e=t.slice()),e[s]=(0,l.addWidgetIdToBlock)(r,i)),e}),t)}))}),[e]);return[t,s,s]}const xe=(0,a.createContext)();function Ie({api:e,sidebarControls:t,children:r}){const[s,i]=(0,a.useState)({current:null}),n=(0,a.useCallback)((e=>{for(const r of t){if(r.setting.get().includes(e)){r.sectionInstance.expand({completeCallback(){i({current:e})}});break}}}),[t]);(0,a.useEffect)((()=>{function t(e){const t=ve(e);n(t)}let r=!1;function s(){e.previewer.preview.bind("focus-control-for-setting",t),r=!0}return e.previewer.bind("ready",s),()=>{e.previewer.unbind("ready",s),r&&e.previewer.preview.unbind("focus-control-for-setting",t)}}),[e,n]);const c=(0,a.useMemo)((()=>[s,n]),[s,n]);return(0,o.createElement)(xe.Provider,{value:c},r)}const ze=()=>(0,a.useContext)(xe);const Ae=window.wp.privateApis,{lock:Be,unlock:We}=(0,Ae.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I know using unstable features means my theme or plugin will inevitably break in the next version of WordPress.","@wordpress/customize-widgets"),{ExperimentalBlockEditorProvider:Me}=We(g.privateApis);function Oe({sidebar:e,settings:t,children:r}){const[s,i,n]=Se(e);return function(e){const{selectBlock:t}=(0,u.useDispatch)(g.store),[r]=ze(),s=(0,a.useRef)(e);(0,a.useEffect)((()=>{s.current=e}),[e]),(0,a.useEffect)((()=>{if(r.current){const e=s.current.find((e=>(0,l.getWidgetIdFromBlock)(e)===r.current));if(e){t(e.clientId);const r=document.querySelector(`[data-block="${e.clientId}"]`);r?.focus()}}}),[r,t])}(s),(0,o.createElement)(Me,{value:s,onInput:i,onChange:n,settings:t,useSubRegistry:!1},r)}function Te({sidebar:e}){const{toggle:t}=(0,u.useDispatch)(m.store),r=e.getWidgets().every((e=>e.id.startsWith("block-")));return(0,o.createElement)("div",{className:"customize-widgets-welcome-guide"},(0,o.createElement)("div",{className:"customize-widgets-welcome-guide__image__wrapper"},(0,o.createElement)("picture",null,(0,o.createElement)("source",{srcSet:"https://s.w.org/images/block-editor/welcome-editor.svg",media:"(prefers-reduced-motion: reduce)"}),(0,o.createElement)("img",{className:"customize-widgets-welcome-guide__image",src:"https://s.w.org/images/block-editor/welcome-editor.gif",width:"312",height:"240",alt:""}))),(0,o.createElement)("h1",{className:"customize-widgets-welcome-guide__heading"},(0,p.__)("Welcome to block Widgets")),(0,o.createElement)("p",{className:"customize-widgets-welcome-guide__text"},r?(0,p.__)("Your theme provides different “block” areas for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site."):(0,p.__)("You can now add any block to your site’s widget areas. Don’t worry, all of your favorite widgets still work flawlessly.")),(0,o.createElement)(h.Button,{className:"customize-widgets-welcome-guide__button",variant:"primary",onClick:()=>t("core/customize-widgets","welcomeGuide")},(0,p.__)("Got it")),(0,o.createElement)("hr",{className:"customize-widgets-welcome-guide__separator"}),!r&&(0,o.createElement)("p",{className:"customize-widgets-welcome-guide__more-info"},(0,p.__)("Want to stick with the old widgets?"),(0,o.createElement)("br",null),(0,o.createElement)(h.ExternalLink,{href:(0,p.__)("https://wordpress.org/plugins/classic-widgets/")},(0,p.__)("Get the Classic Widgets plugin."))),(0,o.createElement)("p",{className:"customize-widgets-welcome-guide__more-info"},(0,p.__)("New to the block editor?"),(0,o.createElement)("br",null),(0,o.createElement)(h.ExternalLink,{href:(0,p.__)("https://wordpress.org/documentation/article/wordpress-block-editor/")},(0,p.__)("Here's a detailed guide."))))}function Ne({undo:e,redo:t,save:r}){const{replaceBlocks:s}=(0,u.useDispatch)(g.store),{getBlockName:i,getSelectedBlockClientId:n,getBlockAttributes:o}=(0,u.useSelect)(g.store),a=(e,t)=>{e.preventDefault();const r=0===t?"core/paragraph":"core/heading",a=n();if(null===a)return;const c=i(a);if("core/paragraph"!==c&&"core/heading"!==c)return;const l=o(a),u="core/paragraph"===c?"align":"textAlign",m="core/paragraph"===r?"align":"textAlign";s(a,(0,d.createBlock)(r,{level:t,content:l.content,[m]:l[u]}))};return(0,R.useShortcut)("core/customize-widgets/undo",(t=>{e(),t.preventDefault()})),(0,R.useShortcut)("core/customize-widgets/redo",(e=>{t(),e.preventDefault()})),(0,R.useShortcut)("core/customize-widgets/save",(e=>{e.preventDefault(),r()})),(0,R.useShortcut)("core/customize-widgets/transform-heading-to-paragraph",(e=>a(e,0))),[1,2,3,4,5,6].forEach((e=>{(0,R.useShortcut)(`core/customize-widgets/transform-paragraph-to-heading-${e}`,(t=>a(t,e)))})),null}Ne.Register=function(){const{registerShortcut:e,unregisterShortcut:t}=(0,u.useDispatch)(R.store);return(0,a.useEffect)((()=>(e({name:"core/customize-widgets/undo",category:"global",description:(0,p.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/customize-widgets/redo",category:"global",description:(0,p.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"},aliases:(0,S.isAppleOS)()?[]:[{modifier:"primary",character:"y"}]}),e({name:"core/customize-widgets/save",category:"global",description:(0,p.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),e({name:"core/customize-widgets/transform-heading-to-paragraph",category:"block-library",description:(0,p.__)("Transform heading to paragraph."),keyCombination:{modifier:"access",character:"0"}}),[1,2,3,4,5,6].forEach((t=>{e({name:`core/customize-widgets/transform-paragraph-to-heading-${t}`,category:"block-library",description:(0,p.__)("Transform paragraph to heading."),keyCombination:{modifier:"access",character:`${t}`}})})),()=>{t("core/customize-widgets/undo"),t("core/customize-widgets/redo"),t("core/customize-widgets/save")})),[e]),null};const Pe=Ne;function Fe(e){const t=(0,a.useRef)(),r=(0,u.useSelect)((e=>0===e(g.store).getBlockCount()));return(0,a.useEffect)((()=>{if(r&&t.current){const{ownerDocument:e}=t.current;e.activeElement&&e.activeElement!==e.body||t.current.focus()}}),[r]),(0,o.createElement)(g.ButtonBlockAppender,{...e,ref:t})}const{ExperimentalBlockCanvas:Le}=We(g.privateApis);function De({blockEditorSettings:e,sidebar:t,inserter:r,inspector:s}){const[i,n]=function(e){const t=(0,u.useSelect)((e=>e(F).isInserterOpened()),[]),{setIsInserterOpened:r}=(0,u.useDispatch)(F);return(0,a.useEffect)((()=>{t?e.open():e.close()}),[e,t]),[t,(0,a.useCallback)((e=>{let t=e;"function"==typeof e&&(t=e((0,u.select)(F).isInserterOpened())),r(t)}),[r])]}(r),c=(0,f.useViewportMatch)("small"),{hasUploadPermissions:l,isFixedToolbarActive:d,keepCaretInsideBlock:h,isWelcomeGuideActive:p}=(0,u.useSelect)((e=>{var t;const{get:r}=e(m.store);return{hasUploadPermissions:null===(t=e(y.store).canUser("create","media"))||void 0===t||t,isFixedToolbarActive:!!r("core/customize-widgets","fixedToolbar"),keepCaretInsideBlock:!!r("core/customize-widgets","keepCaretInsideBlock"),isWelcomeGuideActive:!!r("core/customize-widgets","welcomeGuide")}}),[]),b=(0,a.useMemo)((()=>{let t;return l&&(t=({onError:t,...r})=>{(0,E.uploadMedia)({wpAllowedMimeTypes:e.allowedMimeTypes,onError:({message:e})=>t(e),...r})}),{...e,__experimentalSetIsInserterOpened:n,mediaUpload:t,hasFixedToolbar:d||!c,keepCaretInsideBlock:h,__unstableHasCustomAppender:!0}}),[l,e,d,c,h,n]);return p?(0,o.createElement)(Te,{sidebar:t}):(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Pe.Register,null),(0,o.createElement)(Oe,{sidebar:t,settings:b},(0,o.createElement)(Pe,{undo:t.undo,redo:t.redo,save:t.save}),(0,o.createElement)(be,{sidebar:t,inserter:r,isInserterOpened:i,setIsInserterOpened:n,isFixedToolbarActive:d||!c}),(d||!c)&&(0,o.createElement)(g.BlockToolbar,{hideDragHandle:!0}),(0,o.createElement)(Le,{shouldIframe:!1,styles:b.defaultEditorStyles,height:"100%"},(0,o.createElement)(g.BlockList,{renderAppender:Fe})),(0,a.createPortal)((0,o.createElement)("form",{onSubmit:e=>e.preventDefault()},(0,o.createElement)(g.BlockInspector,null)),s.contentContainer[0])),(0,o.createElement)(g.__unstableBlockSettingsMenuFirstItem,null,(({onClose:e})=>(0,o.createElement)(v,{inspector:s,closeMenu:e}))))}const Re=(0,a.createContext)();function Ve({sidebarControls:e,activeSidebarControl:t,children:r}){const s=(0,a.useMemo)((()=>({sidebarControls:e,activeSidebarControl:t})),[e,t]);return(0,o.createElement)(Re.Provider,{value:s},r)}function He({api:e,sidebarControls:t,blockEditorSettings:r}){const[s,i]=(0,a.useState)(null),n=document.getElementById("customize-theme-controls"),c=(0,a.useRef)();!function(e,t){const{hasSelectedBlock:r,hasMultiSelection:s}=(0,u.useSelect)(g.store),{clearSelectedBlock:i}=(0,u.useDispatch)(g.store);(0,a.useEffect)((()=>{if(t.current&&e){const n=e.inspector,o=e.container[0],a=o.ownerDocument,c=a.defaultView;function l(e){!r()&&!s()||!e||!a.contains(e)||o.contains(e)||t.current.contains(e)||e.closest('[role="dialog"]')||n.expanded()||i()}function d(e){l(e.target)}function u(){l(a.activeElement)}return a.addEventListener("mousedown",d),c.addEventListener("blur",u),()=>{a.removeEventListener("mousedown",d),c.removeEventListener("blur",u)}}}),[t,e,r,s,i])}(s,c),(0,a.useEffect)((()=>{const e=t.map((e=>e.subscribe((t=>{t&&i(e)}))));return()=>{e.forEach((e=>e()))}}),[t]);const l=s&&(0,a.createPortal)((0,o.createElement)(_,null,(0,o.createElement)(De,{key:s.id,blockEditorSettings:r,sidebar:s.sidebarAdapter,inserter:s.inserter,inspector:s.inspector})),s.container[0]),d=n&&(0,a.createPortal)((0,o.createElement)("div",{className:"customize-widgets-popover",ref:c},(0,o.createElement)(h.Popover.Slot,null)),n);return(0,o.createElement)(h.SlotFillProvider,null,(0,o.createElement)(Ve,{sidebarControls:t,activeSidebarControl:s},(0,o.createElement)(Ie,{api:e,sidebarControls:t},l,d)))}const Ge=e=>`widgets-inspector-${e}`;function Ue(){const{wp:{customize:e}}=window,t=window.matchMedia("(prefers-reduced-motion: reduce)");let r=t.matches;return t.addEventListener("change",(e=>{r=e.matches})),class extends e.Section{ready(){const t=function(){const{wp:{customize:e}}=window;return class extends e.Section{constructor(e,t){super(e,t),this.parentSection=t.parentSection,this.returnFocusWhenClose=null,this._isOpen=!1}get isOpen(){return this._isOpen}set isOpen(e){this._isOpen=e,this.triggerActiveCallbacks()}ready(){this.contentContainer[0].classList.add("customize-widgets-layout__inspector")}isContextuallyActive(){return this.isOpen}onChangeExpanded(e,t){super.onChangeExpanded(e,t),this.parentSection&&!t.unchanged&&(e?this.parentSection.collapse({manualTransition:!0}):this.parentSection.expand({manualTransition:!0,completeCallback:()=>{this.returnFocusWhenClose&&!this.contentContainer[0].contains(this.returnFocusWhenClose)&&this.returnFocusWhenClose.focus()}}))}open({returnFocusWhenClose:e}={}){this.isOpen=!0,this.returnFocusWhenClose=e,this.expand({allowMultiple:!0})}close(){this.collapse({allowMultiple:!0})}collapse(e){this.isOpen=!1,super.collapse(e)}triggerActiveCallbacks(){this.active.callbacks.fireWith(this.active,[!1,!0])}}}();this.inspector=new t(Ge(this.id),{title:(0,p.__)("Block Settings"),parentSection:this,customizeAction:[(0,p.__)("Customizing"),(0,p.__)("Widgets"),this.params.title].join(" ▸ ")}),e.section.add(this.inspector),this.contentContainer[0].classList.add("customize-widgets__sidebar-section")}hasSubSectionOpened(){return this.inspector.expanded()}onChangeExpanded(e,t){const s=this.controls(),i={...t,completeCallback(){s.forEach((t=>{t.onChangeSectionExpanded?.(e,i)})),t.completeCallback?.()}};if(i.manualTransition){e?(this.contentContainer.addClass(["busy","open"]),this.contentContainer.removeClass("is-sub-section-open"),this.contentContainer.closest(".wp-full-overlay").addClass("section-open")):(this.contentContainer.addClass(["busy","is-sub-section-open"]),this.contentContainer.closest(".wp-full-overlay").addClass("section-open"),this.contentContainer.removeClass("open"));const t=()=>{this.contentContainer.removeClass("busy"),i.completeCallback()};r?t():this.contentContainer.one("transitionend",t)}else super.onChangeExpanded(e,i)}}}const{wp:$e}=window;function je(e){const t=e.match(/^(.+)-(\d+)$/);return t?{idBase:t[1],number:parseInt(t[2],10)}:{idBase:e}}function Ye(e){const{idBase:t,number:r}=je(e);return r?`widget_${t}[${r}]`:`widget_${t}`}class qe{constructor(e,t){this.setting=e,this.api=t,this.locked=!1,this.widgetsCache=new WeakMap,this.subscribers=new Set,this.history=[this._getWidgetIds().map((e=>this.getWidget(e)))],this.historyIndex=0,this.historySubscribers=new Set,this._debounceSetHistory=function(e,t,r){let s,i=!1;function n(...n){const o=(i?t:e).apply(this,n);return i=!0,clearTimeout(s),s=setTimeout((()=>{i=!1}),r),o}return n.cancel=()=>{i=!1,clearTimeout(s)},n}(this._pushHistory,this._replaceHistory,1e3),this.setting.bind(this._handleSettingChange.bind(this)),this.api.bind("change",this._handleAllSettingsChange.bind(this)),this.undo=this.undo.bind(this),this.redo=this.redo.bind(this),this.save=this.save.bind(this)}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}getWidgets(){return this.history[this.historyIndex]}_emit(...e){for(const t of this.subscribers)t(...e)}_getWidgetIds(){return this.setting.get()}_pushHistory(){this.history=[...this.history.slice(0,this.historyIndex+1),this._getWidgetIds().map((e=>this.getWidget(e)))],this.historyIndex+=1,this.historySubscribers.forEach((e=>e()))}_replaceHistory(){this.history[this.historyIndex]=this._getWidgetIds().map((e=>this.getWidget(e)))}_handleSettingChange(){if(this.locked)return;const e=this.getWidgets();this._pushHistory(),this._emit(e,this.getWidgets())}_handleAllSettingsChange(e){if(this.locked)return;if(!e.id.startsWith("widget_"))return;const t=ve(e.id);if(!this.setting.get().includes(t))return;const r=this.getWidgets();this._pushHistory(),this._emit(r,this.getWidgets())}_createWidget(e){const t=$e.customize.Widgets.availableWidgets.findWhere({id_base:e.idBase});let r=e.number;t.get("is_multi")&&!r&&(t.set("multi_number",t.get("multi_number")+1),r=t.get("multi_number"));const s=r?`widget_${e.idBase}[${r}]`:`widget_${e.idBase}`,i={transport:$e.customize.Widgets.data.selectiveRefreshableWidgets[t.get("id_base")]?"postMessage":"refresh",previewer:this.setting.previewer};this.api.create(s,s,"",i).set(e.instance);return ve(s)}_removeWidget(e){const t=Ye(e.id),r=this.api(t);if(r){const e=r.get();this.widgetsCache.delete(e)}this.api.remove(t)}_updateWidget(e){const t=this.getWidget(e.id);if(t===e)return e.id;if(t.idBase&&e.idBase&&t.idBase===e.idBase){const t=Ye(e.id);return this.api(t).set(e.instance),e.id}return this._removeWidget(e),this._createWidget(e)}getWidget(e){if(!e)return null;const{idBase:t,number:r}=je(e),s=Ye(e),i=this.api(s);if(!i)return null;const n=i.get();if(this.widgetsCache.has(n))return this.widgetsCache.get(n);const o={id:e,idBase:t,number:r,instance:n};return this.widgetsCache.set(n,o),o}_updateWidgets(e){this.locked=!0;const t=[],r=e.map((e=>{if(e.id&&this.getWidget(e.id))return t.push(null),this._updateWidget(e);const r=this._createWidget(e);return t.push(r),r}));return this.getWidgets().filter((e=>!r.includes(e.id))).forEach((e=>this._removeWidget(e))),this.setting.set(r),this.locked=!1,t}setWidgets(e){const t=this._updateWidgets(e);return this._debounceSetHistory(),t}hasUndo(){return this.historyIndex>0}hasRedo(){return this.historyIndex<this.history.length-1}_seek(e){const t=this.getWidgets();this.historyIndex=e;const r=this.history[this.historyIndex];this._updateWidgets(r),this._emit(t,this.getWidgets()),this.historySubscribers.forEach((e=>e())),this._debounceSetHistory.cancel()}undo(){this.hasUndo()&&this._seek(this.historyIndex-1)}redo(){this.hasRedo()&&this._seek(this.historyIndex+1)}subscribeHistory(e){return this.historySubscribers.add(e),()=>{this.historySubscribers.delete(e)}}save(){this.api.previewer.save()}}const Ke=window.wp.dom;const Ze=e=>`widgets-inserter-${e}`;function Je(){const{wp:{customize:e}}=window;return class extends e.Control{constructor(...e){super(...e),this.subscribers=new Set}ready(){const t=function(){const{wp:{customize:e}}=window,t=e.OuterSection;return e.OuterSection=class extends t{onChangeExpanded(t,r){return t&&e.section.each((e=>{"outer"===e.params.type&&e.id!==this.id&&e.expanded()&&e.collapse()})),super.onChangeExpanded(t,r)}},e.sectionConstructor.outer=e.OuterSection,class extends e.OuterSection{constructor(...e){super(...e),this.params.type="outer",this.activeElementBeforeExpanded=null,this.contentContainer[0].ownerDocument.defaultView.addEventListener("keydown",(e=>{!this.expanded()||e.keyCode!==S.ESCAPE&&"Escape"!==e.code||e.defaultPrevented||(e.preventDefault(),e.stopPropagation(),(0,u.dispatch)(F).setIsInserterOpened(!1))}),!0),this.contentContainer.addClass("widgets-inserter"),this.isFromInternalAction=!1,this.expanded.bind((()=>{this.isFromInternalAction||(0,u.dispatch)(F).setIsInserterOpened(this.expanded()),this.isFromInternalAction=!1}))}open(){if(!this.expanded()){const e=this.contentContainer[0];this.activeElementBeforeExpanded=e.ownerDocument.activeElement,this.isFromInternalAction=!0,this.expand({completeCallback(){const t=Ke.focus.tabbable.find(e)[1];t&&t.focus()}})}}close(){if(this.expanded()){const e=this.contentContainer[0],t=e.ownerDocument.activeElement;this.isFromInternalAction=!0,this.collapse({completeCallback(){e.contains(t)&&this.activeElementBeforeExpanded&&this.activeElementBeforeExpanded.focus()}})}}}}();this.inserter=new t(Ze(this.id),{}),e.section.add(this.inserter),this.sectionInstance=e.section(this.section()),this.inspector=this.sectionInstance.inspector,this.sidebarAdapter=new qe(this.setting,e)}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}onChangeSectionExpanded(e,t){t.unchanged||(e||(0,u.dispatch)(F).setIsInserterOpened(!1),this.subscribers.forEach((r=>r(e,t))))}}}const Qe=(0,f.createHigherOrderComponent)((e=>t=>{let r=(0,l.getWidgetIdFromBlock)(t);const s=function(){const{sidebarControls:e}=(0,a.useContext)(Re);return e}(),i=function(){const{activeSidebarControl:e}=(0,a.useContext)(Re);return e}(),n=s?.length>1,c=t.name,d=t.clientId,m=(0,u.useSelect)((e=>e(g.store).canInsertBlockType(c,"")),[c]),h=(0,u.useSelect)((e=>e(g.store).getBlock(d)),[d]),{removeBlock:p}=(0,u.useDispatch)(g.store),[,f]=ze();return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(e,{...t}),n&&m&&(0,o.createElement)(g.BlockControls,null,(0,o.createElement)(l.MoveToWidgetArea,{widgetAreas:s.map((e=>({id:e.id,name:e.params.label,description:e.params.description}))),currentWidgetAreaId:i?.id,onSelect:function(e){const t=s.find((t=>t.id===e));if(r){const e=i.setting,s=t.setting;e(e().filter((e=>e!==r))),s([...s(),r])}else{const e=t.sidebarAdapter;p(d);const s=e.setWidgets([...e.getWidgets(),ke(h)]);r=s.reverse().find((e=>!!e))}f(r)}})))}),"withMoveToSidebarToolbarItem");(0,b.addFilter)("editor.BlockEdit","core/customize-widgets/block-edit",Qe);(0,b.addFilter)("editor.MediaUpload","core/edit-widgets/replace-media-upload",(()=>E.MediaUpload));const{wp:Xe}=window,et=(0,f.createHigherOrderComponent)((e=>t=>{var r;const{idBase:s}=t.attributes,i=null!==(r=Xe.customize.Widgets.data.availableWidgets.find((e=>e.id_base===s))?.is_wide)&&void 0!==r&&r;return(0,o.createElement)(e,{...t,isWide:i})}),"withWideWidgetDisplay");(0,b.addFilter)("editor.BlockEdit","core/customize-widgets/wide-widget-display",et);const{wp:tt}=window,rt=["core/more","core/block","core/freeform","core/template-part"];function st(e,t){(0,u.dispatch)(m.store).setDefaults("core/customize-widgets",{fixedToolbar:!1,welcomeGuide:!0}),(0,u.dispatch)(d.store).reapplyBlockTypeFilters();const r=(0,c.__experimentalGetCoreBlocks)().filter((e=>!(rt.includes(e.name)||e.name.startsWith("core/post")||e.name.startsWith("core/query")||e.name.startsWith("core/site")||e.name.startsWith("core/navigation"))));(0,c.registerCoreBlocks)(r),(0,l.registerLegacyWidgetBlock)(),(0,l.registerLegacyWidgetVariations)(t),(0,l.registerWidgetGroupBlock)(),(0,d.setFreeformContentHandlerName)("core/html");const s=Je();tt.customize.sectionConstructor.sidebar=Ue(),tt.customize.controlConstructor.sidebar_block_editor=s;const i=document.createElement("div");document.body.appendChild(i),tt.customize.bind("ready",(()=>{const e=[];tt.customize.control.each((t=>{t instanceof s&&e.push(t)})),(0,a.createRoot)(i).render((0,o.createElement)(He,{api:tt.customize,sidebarControls:e,blockEditorSettings:t}))}))}})(),(window.wp=window.wp||{}).customizeWidgets=s})();